import React, { useState, useEffect } from "react";
import { format } from 'date-fns';
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";



const SubscriptionTab = ({ userCredits }) => {
    console.log("userCredits", userCredits);

    // State for transaction history
    const [transactionHistory, setTransactionHistory] = useState([]);
    const [isLoadingTransactions, setIsLoadingTransactions] = useState(false);
    const [transactionError, setTransactionError] = useState("");

    // Fetch transaction history
    const fetchTransactionHistory = async () => {
        console.log("🔄 fetchTransactionHistory: Function called - starting transaction history fetch");

        setIsLoadingTransactions(true);
        setTransactionError("");

        const params = {
            page: 1,
            pageSize: 7,
            sortBy: "desc",
            searchParams: {
                transaction_status: "paid"
            }
        };

        try {
            setIsLoadingTransactions(true);
            setTransactionError("");

            const response = await PostWithTokenNoCache(ApiName.transactionHistory, params);

            if (response && response.data) {
                // Check if response has status field, if not assume success (200 status from HTTP)
                const hasStatusField = 'status' in response.data;
                const isSuccess = hasStatusField ? response.data.status === 200 : response.status === 200;

                if (isSuccess) {
                    // Try response.data.data first (nested data), then response.data directly
                    let rawTransactionData = response.data.data;

                    // If response.data.data doesn't exist or is null, use response.data directly
                    if (!rawTransactionData) {
                        rawTransactionData = response.data;
                    }

                    // Handle JSON string response - parse if data is a string
                    if (typeof rawTransactionData === 'string') {
                        try {
                            rawTransactionData = JSON.parse(rawTransactionData);
                        } catch (parseError) {
                            console.error("Failed to parse transaction data JSON:", parseError);
                            setTransactionError("Failed to parse transaction data");
                            setIsLoadingTransactions(false);
                            return;
                        }
                    }

                    // Handle different possible data structures
                    let transactionData = [];

                    if (Array.isArray(rawTransactionData)) {
                        // Data is already an array
                        transactionData = rawTransactionData;
                    } else if (rawTransactionData && typeof rawTransactionData === 'object') {

                        if (Array.isArray(rawTransactionData.transactions)) {
                            transactionData = rawTransactionData.transactions;
                        } else if (Array.isArray(rawTransactionData.data)) {
                            transactionData = rawTransactionData.data;
                        } else if (Array.isArray(rawTransactionData.items)) {
                            transactionData = rawTransactionData.items;
                        } else if (Array.isArray(rawTransactionData.records)) {
                            transactionData = rawTransactionData.records;
                        } else if (Array.isArray(rawTransactionData.results)) {
                            transactionData = rawTransactionData.results;
                        } else if (Array.isArray(rawTransactionData.list)) {
                            transactionData = rawTransactionData.list;
                        } else {
                            // Check if any property contains an array
                            const arrayProperty = Object.keys(rawTransactionData).find(key =>
                                Array.isArray(rawTransactionData[key])
                            );

                            if (arrayProperty) {
                                transactionData = rawTransactionData[arrayProperty];
                            } else {
                                // Single transaction object, wrap in array
                                transactionData = [rawTransactionData];
                            }
                        }
                    } else {
                        // Final fallback - check directly for items property
                        if (rawTransactionData && rawTransactionData.items && Array.isArray(rawTransactionData.items)) {
                            transactionData = rawTransactionData.items;
                        } else {
                            console.warn("Could not parse transaction data, using empty array");
                            transactionData = [];
                        }
                    }

                    // Transform API data to UI format
                    const transformedTransactions = Array.isArray(transactionData) ? transactionData.map((apiTransaction) => {
                        const transformedTransaction = {
                            // Map API fields to UI expected fields
                            invoice_date: apiTransaction.transaction_createdAt || apiTransaction.created_at || apiTransaction.date,
                            invoice_id: apiTransaction.transaction_id || apiTransaction.id || apiTransaction.invoice_id,
                            status: apiTransaction.transaction_status || apiTransaction.status,
                            amount: apiTransaction.transaction_amount || apiTransaction.amount
                        };

                        return transformedTransaction;
                    }) : [];

                    // Log final transformed data for production monitoring
                    console.log("Transaction history loaded:", {
                        count: transformedTransactions.length,
                        transactions: transformedTransactions.map(t => ({
                            invoice_date: t.invoice_date,
                            invoice_id: t.invoice_id,
                            status: t.status,
                            amount: t.amount
                        }))
                    });

                    setTransactionHistory(transformedTransactions);
                    setTransactionError("");
                } else {
                    console.warn("Transaction history API returned non-200 status:", response.data.status);
                    setTransactionError("Failed to load transaction history");
                }
            } else {
                console.warn("Invalid response structure from transaction history API");
                setTransactionError("Failed to load transaction history");
            }
        } catch (error) {
            console.error("Transaction history API error:", error);
            console.error("❌ fetchTransactionHistory: Error response status:", error?.response?.status);
            console.error("❌ fetchTransactionHistory: Error message:", error?.response?.data?.message);

            const message = error?.response?.data?.message || "Failed to fetch transaction history";
            setTransactionError(message);
            console.error("❌ fetchTransactionHistory: Final error message set:", message);
        } finally {
            setIsLoadingTransactions(false);
            console.log("🏁 fetchTransactionHistory: Function completed - loading state set to false");
        }
    };

    // Fetch transaction history on component mount
    useEffect(() => {
        fetchTransactionHistory();
    }, []);

    // Helper function to format credit values
    const formatCreditValue = (value) => {
        if (value === null || value === undefined) return "0";
        if (typeof value === "number") return value.toLocaleString("en-IN");
        if (typeof value === "string" && value.toLowerCase() === "unlimited")
            return "Unlimited";
        return value;
    };

    // Helper function to format transaction date
    const formatTransactionDate = (dateString) => {
        if (!dateString) return "N/A";
        try {
            // Handle ISO date format from API (e.g., "2025-07-31T04:40:33.621+00:00")
            const date = new Date(dateString);
            return format(date, 'M/d/yyyy');
        } catch (error) {
            console.warn("⚠️ formatTransactionDate: Error formatting date:", dateString, error);
            return dateString;
        }
    };

    // Helper function to format amount
    const formatAmount = (amount) => {
        if (!amount) return "–";
        try {
            // Handle string amounts from API (e.g., "58800" represents $588.00)
            let numericAmount;

            if (typeof amount === 'string') {
                // Convert string to number and divide by 100 to get dollars from cents
                numericAmount = parseFloat(amount) / 100;
            } else if (typeof amount === 'number') {
                // If already a number, check if it needs to be divided by 100
                // Assume amounts over 1000 are in cents
                numericAmount = amount > 1000 ? amount / 100 : amount;
            } else {
                console.warn("Unknown amount format:", amount);
                return "–";
            }

            return `$${numericAmount.toFixed(2)}`;
        } catch (error) {
            console.warn("Error formatting amount:", amount, error);
            return "–";
        }
    };

    // Parse the plan name to make it more readable
    const parsePlanName = (planName) => {
        if (!planName) return "Free";
        return planName
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Format the period end date
    const formatDate = (dateString) => {
        if (!dateString) return "N/A";
        try {
            return format(new Date(dateString), 'MMM d, yyyy');
        } catch {
            return dateString;
        }
    };

    // Determine if the plan is yearly based on package_name and billing_period
    const isYearlyPlan = () => {
        if (!userCredits || !userCredits.user_plan_name) return false;

        // Check if plan name contains "yearly" (case insensitive)
        const isYearlyInName = userCredits.user_plan_name.toLowerCase().includes('yearly');

        // If billing_period is available in userCredits, use that
        if (userCredits.billing_period) {
            return userCredits.billing_period.toLowerCase() === 'yearly';
        }

        // Fallback to checking the plan name
        return isYearlyInName;
    };

    return (
        <div className="profile-box-2">
            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box">
                        <div className="my-sub">
                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="my-second-subscription">Subscription</p>
                                </div>
                                <div>
                                    <button type="button" className="my-upgrade">Upgrade</button>
                                </div>
                            </div>

                            <div className="sub-table-border">
                                <div className="current-plan">
                                    <p className="my-current plan">Current Plan</p>
                                </div>
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="my-freemium">
                                            {userCredits ? parsePlanName(userCredits.user_plan_name) : "Loading..."}
                                        </p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="check-my-views">
                                            <img src="images/account-credits-view-icon.png" alt="Views" />
                                            {userCredits
                                                ? userCredits.total_assigned_contact_view === "Unlimited"
                                                    ? "Unlimited Profile Views"
                                                    : `${formatCreditValue(userCredits.total_balance_contact_view)}/${formatCreditValue(userCredits.total_assigned_contact_view)} Profile Views`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="my-freemium">
                                            {userCredits?.period_end
                                                ? `Renews on ${formatDate(userCredits.period_end)}`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="check-my-views">
                                            <img src="images/accounts-credits-download.png" alt="Downloads" />
                                            {userCredits
                                                ? `${formatCreditValue(userCredits.total_assigned_credit)} ${isYearlyPlan() ? 'Yearly' : 'Monthly'} Downloads`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>


            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box mt-3">
                        <div className="my-sub">
                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="my-second-subscription">Credit Card Information</p>
                                </div>
                                <div>
                                    <button type="button" className="my-upgrade">Update Credit Card</button>
                                </div>
                            </div>

                            <div className="row">
                                <div className="col-md-7">
                                    <div className="sub-table-border">
                                        <div className="current-plan">
                                            <p className="my-current plan">Card Information</p>
                                        </div>
                                        <div className="row">
                                            <div className="col-md-12">
                                                <p className="my-freemium">
                                                    Card Number: **** **** **** 4800<br />
                                                    Expiration Date: 3/2028
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-5">
                                    <p className="my-billing">Billing Address on Card</p>
                                    <p className="my-moqdom">
                                        Maqdoom Syed<br />
                                        3080, Olcott St, Ste B220<br />
                                        Santa Clara, California 95054<br />
                                        US
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box mt-3">
                        <div className="my-sub">
                            <div className="invoice-container">
                                <div className="invoice-vertical-scroll">
                                    <table className="invoice-table">
                                        <thead>
                                            <tr>
                                                <th>Invoice Date</th>
                                                <th>Invoice ID</th>
                                                <th>Status</th>
                                                <th>Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {isLoadingTransactions ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                                                        Loading transaction history...
                                                    </td>
                                                </tr>
                                            ) : transactionError ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                                                        {transactionError}
                                                    </td>
                                                </tr>
                                            ) : transactionHistory.length === 0 ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                                                        No transaction history found
                                                    </td>
                                                </tr>
                                            ) : (
                                                transactionHistory.map((transaction, idx) => (
                                                    <tr key={transaction.invoice_id || idx}>
                                                        <td>{formatTransactionDate(transaction.invoice_date)}</td>
                                                        <td>{transaction.invoice_id || 'N/A'}</td>
                                                        <td className="status paid">{transaction.status || 'Paid'}</td>
                                                        <td>{formatAmount(transaction.amount)}</td>
                                                    </tr>
                                                ))
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

        </div>
    );
};

export default SubscriptionTab;