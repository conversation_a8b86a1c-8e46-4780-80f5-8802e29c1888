import React, { useEffect, useRef, useState } from "react";
import UseTabStore from "../common-files/useGlobalState";
import { useNavigate } from "react-router-dom";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";

const CreateListPopup = ({ getAllList, setIsOpenCreateList, setIsLoading, currentWishListSection }) => {

    const alertRef = useRef();
    const [listName, setListName] = useState("");
    const {
        selectedTab,
        setButtonType,
        setDefaultErrorMsg,
        setDefaultAlert,
    } = UseTabStore();

    useEffect(() => {
        alertRef.current.click();
    }, [])

    const createNewList = async () => {
        if (listName.length > 1) {
            // Use the passed currentWishListSection instead of global selectedTab
            const wishListSection = currentWishListSection || (selectedTab == "contact" ? "CONTACT" : "COMPANY");

            const param = JSON.stringify({
                listName: listName,
                status: "OPEN",
                wishListSection: wishListSection,
                listCount: 0,
            });
            try {
                const res = await PostWithTokenNoCache(ApiName.createList, param);
                if (res && "status" in res) {
                    if (res.status == 200) {
                        setIsOpenCreateList(false);
                        // Call getAllList if it's provided
                        if (getAllList) {
                            getAllList();
                        }
                    } else {
                        setButtonType("error");
                        setDefaultErrorMsg(res.response.data.message);
                        setDefaultAlert(true);
                    }
                } else {
                    setButtonType("error");
                    setDefaultErrorMsg(res.response.data.message);
                    setDefaultAlert(true);
                }
            } catch (error) {
                setButtonType("error");
                setDefaultErrorMsg(error?.response?.data?.message);
                setDefaultAlert(true);
            }
        }
    };

    const close = () => {
        setIsOpenCreateList(false);
    }
    return (
        <>
            <button
                type="button"
                className="createnewList"
                data-toggle="modal"
                data-target="#exampleModal"
                ref={alertRef}
                style={{ display: 'none' }}
            >
                Create New List
            </button>
            <div
                className="modal fade"
                id="exampleModal"
                tabindex="-1"
                role="dialog"
                aria-labelledby="exampleModalLabel"
                aria-hidden="true"
            >
                <div className="modal-dialog" role="document">
                    <div
                        className="modal-content"
                        style={{
                            width: "75%",
                            margin: "10rem 7rem 6px",
                            backgroundColor: "#F5F5F5",
                            padding: "0 8px 0 8px",
                        }}
                    >
                        <div className="modal-header" style={{ borderBottom: "0" }}>
                            <h5 className="modal-title" id="exampleModalLabel">
                                Create List
                            </h5>
                            <span className="savecontactsunderline"></span>
                            <button
                                type="button"
                                class="close"
                                data-dismiss="modal"
                                aria-label="Close"
                                onClick={close}
                            >
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div className="modal-body">
                            <p className="AddToNewList">List Name:</p>
                            <div className="form-group">
                                <input
                                    type="text"
                                    className="AddToNewListinput"
                                    aria-describedby="emailHelp"
                                    placeholder="Name Your List..."
                                    value={listName}
                                    onChange={(event) => setListName(event.target.value)}
                                />
                            </div>

                            <div className="d-flex flex-row-reverse">
                                <div>
                                    <button
                                        type="submit"
                                        onClick={createNewList}
                                        className="Savepopup"
                                        style={{
                                            opacity: listName ? "1" : "0.3",
                                        }}
                                        data-dismiss={listName ? "modal" : ""}
                                        aria-label="Close"
                                    >
                                        Save
                                    </button>
                                </div>

                                <div className="mr-2">
                                    <button
                                        onClick={() => setListName("")}
                                        type="submit"
                                        className="cancel"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

export default CreateListPopup;