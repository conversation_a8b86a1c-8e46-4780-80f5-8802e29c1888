import Papa from 'papaparse';
import { useEffect, useRef, useState } from "react";
import { CSVLink } from "react-csv";
import { useNavigate } from "react-router-dom";
import streamSaver from 'streamsaver';
import "../assests/css/filter/saved_list.css";
import "../assests/css/filter/settings.css";
import loadingGif from "../assests/waiting.gif";
import Alert from "../common-files/alert";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import CreditAlert from "../common-files/creditAlert.js";
import UseTabStore from "../common-files/useGlobalState";
import Header from '../layouts/Header.js';
import EmptyList from "../list/EmptyList";
import Pagination from "../pagination/Pagination.js";
import CreateListPopup from "./CreateListPopup.js";
import DeletePopup from "./DeletePopup.js";

const SavedList = () => {
  const [hoveredItemId, setHoveredItemId] = useState(null);
  const csvLinkRef = useRef();
  const pageSize = 10;
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const [isOpenCreateList, setIsOpenCreateList] = useState(false);
  const [downloadedRows, setDownloadedRows] = useState([]); //data
  const [userCredits, setUserCredits] = useState("");
  const [isReveal, setIsReveal] = useState();
  const [csvDownloadMessage, setCsvDownloadMessage] = useState("");
  const [selectedWishList, setSelectedWishList] = useState("CONTACT");
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [activeTab, setActiveTab] = useState('contacts');
  const [editingListId, setEditingListId] = useState(null);
  const [editedListName, setEditedListName] = useState("");
  const [isUpdatingName, setIsUpdatingName] = useState(false);

  const pollingIntervals = useRef({});
  const {
    buttonType,
    selectedTab,
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultErrorMsg,
    setDefaultAlert } = UseTabStore();
  const navigate = useNavigate();

  useEffect(() => {
    return () => {
      // Safely clear all intervals when component unmounts
      if (pollingIntervals.current) {
        Object.values(pollingIntervals.current).forEach(interval => {
          if (interval) clearInterval(interval);
        });
        pollingIntervals.current = {};
      }
    };
  }, []);

  useEffect(() => {
    const creditsUpdate = async () => {
      try {
        const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});
        if (res && "status" in res) {
          if (res.data.status == 200 && "data" in res.data) {
            setUserCredits(JSON.parse(res.data.data).total_balance_credit);
          }
        }
      } catch (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
        setIsLoading(false);
      }
    };
    creditsUpdate();
  }, []);

  useEffect(() => {
    setIsLoading(true);
    selectedTab === "contact" ? setSelectedWishList("CONTACT") : setSelectedWishList("COMPANY")
    getAllList(selectedTab === "contact" ? "CONTACT" : "COMPANY");
  }, [currentPage]);

  useEffect(() => {
    // Add event listeners when the component mounts
    document.addEventListener("mousedown", handleOutsideClick);
    document.addEventListener("keydown", handleEscKeyPress);

    // Remove event listeners when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
      document.removeEventListener("keydown", handleEscKeyPress);
    };
  }, []);

  // Modified getAllList function
  const getAllList = async (wishListSection) => {
    const param = JSON.stringify({
      page: searchQuery ? 1 : currentPage,
      pageSize: searchQuery ? 10000 : pageSize,
      wishListSection
    });

    try {
      const res = await PostWithTokenNoCache(ApiName.fetchAllList, param);
      if (res && "status" in res) {
        if (res.status === 200) {
          const responseData = JSON.parse(res.data.data);
          const record = responseData.wish_list;
          setData(record.items);
          setIsLoading(false);
          setTotalPages(record.totalPages);

          // Safely clear all existing polling intervals
          if (pollingIntervals.current) {
            Object.values(pollingIntervals.current).forEach(interval => {
              if (interval) clearInterval(interval);
            });
            pollingIntervals.current = {};
          }

          // Start polling only for NOT_READY_FOR_VERIFICATION lists on current page
          record.items.forEach(item => {
            if (item.status === "NOT_READY_FOR_VERIFICATION") {
              startPollingList(item.id);
            }
          });
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response?.data?.message || "An error occurred");
          setDefaultAlert(true);
          setIsLoading(false);
        }
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message || "An error occurred");
      setDefaultAlert(true);
      setIsLoading(false);
    }
  };

  // Complete polling function
  const startPollingList = (listId) => {

    // Clear any existing interval for this list
    if (pollingIntervals.current?.[listId]) {
      clearInterval(pollingIntervals.current[listId]);
      delete pollingIntervals.current[listId];
    }

    // Initialize if not exists
    if (!pollingIntervals.current) {
      pollingIntervals.current = {};
    }

    // Create new interval
    pollingIntervals.current[listId] = setInterval(async () => {

      try {
        const res = await PostWithTokenNoCache(ApiName.fetchAllList, JSON.stringify({
          page: currentPage,
          pageSize: pageSize,
          wishListSection: selectedWishList
        }));

        if (res?.status === 200) {
          const responseData = JSON.parse(res.data.data);
          const updatedItems = responseData.wish_list.items;
          const updatedList = updatedItems.find(item => item.id === listId);

          // Update the data state
          setData(prevData =>
            prevData.map(item =>
              item.id === listId ? updatedList || item : item
            )
          );

          // If status changed, stop polling
          if (updatedList?.status !== "NOT_READY_FOR_VERIFICATION") {
            if (pollingIntervals.current?.[listId]) {
              clearInterval(pollingIntervals.current[listId]);
              delete pollingIntervals.current[listId];
            }
          }
        }
      } catch (error) {
        if (pollingIntervals.current?.[listId]) {
          clearInterval(pollingIntervals.current[listId]);
          delete pollingIntervals.current[listId];
        }
      }
    }, 10000); // Poll every 10 seconds
  };

  const dateFormat = (date) => {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = parsedDate.getMonth() + 1;
    const day = parsedDate.getDate();
    const hours = parsedDate.getHours();
    const minutes = parsedDate.getMinutes();
    const ampm = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
    return `${year}/${month}/${day} - ${formattedHours}:${minutes < 10 ? "0" : ""
      }${minutes} ${ampm}`;
  };
  // Modified viewListDetails function to stop polling when navigating away
  const viewListDetails = (id, section) => {
    // Stop polling for this list when viewing details
    if (pollingIntervals.current?.[id]) {
      clearInterval(pollingIntervals.current[id]);
      delete pollingIntervals.current[id];
    }

    if (section == "CONTACT") {
      navigate(`/contact-list/` + id);
    } else {
      navigate(`/company-list/` + id);
    }
  };
  const deleteList = (id) => {
    setIsPopupOpen(true);
    setDeleteId(id);
  };
  const handleOutsideClick = (e) => {
    // Check if the click is outside the modal
    if (!e.target.closest(".modal-content")) {
      setIsPopupOpen(false);
    }
  };
  const handleEscKeyPress = (e) => {
    // Check if the pressed key is ESC
    if (e.key === "Escape") {
      setIsPopupOpen(false);
    }
  };
  const onCloseDelete = () => {
    setIsPopupOpen(false);
  };
  const onDeleteSuccess = async () => {
    setIsPopupOpen(false);
    const param = JSON.stringify({
      id: deleteId,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.deleteList, param);
      if (res && "status" in res) {
        if (res.status == 200) {
          setIsLoading(true);
          getAllList(selectedWishList);
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const goBack = () => {
    if (selectedTab == "contact") {
      navigate("/search");
    } else {
      navigate("/company-filters");
    }
  };

  const createList = () => {
    if (selectedWishList == "CONTACT") {
      navigate("/search");
    } else {
      navigate("/company-filters");
    }
  }

  const handleEditClick = (item) => {
    setEditingListId(item.id);
    setEditedListName(item.listName);
  };

  const handleCancelEdit = () => {
    setEditingListId(null);
    setEditedListName("");
  };

  const handleSaveEdit = async (id) => {
    if (!editedListName.trim()) {
      setButtonType("error");
      setDefaultErrorMsg("List name cannot be empty");
      setDefaultAlert(true);
      return;
    }

    setIsUpdatingName(true);

    try {
      // Find the current item to get all required fields
      const currentItem = data.find(item => item.id === id);
      if (!currentItem) {
        setButtonType("error");
        setDefaultErrorMsg("List not found");
        setDefaultAlert(true);
        setIsUpdatingName(false);
        return;
      }

      const params = {
        id: id,
        listName: editedListName.trim(),
        wishListSection: currentItem.wishListSection,
        status: currentItem.status,
     
      };

      const response = await PostWithTokenNoCache(ApiName.updateList, params);

      if (response && response.data && response.data.status === 200) {
        // Update the local data
      getAllList(selectedWishList);

        setEditingListId(null);
        setEditedListName("");

        setButtonType("success");
        setDefaultErrorMsg("List name updated successfully");
        setDefaultAlert(true);
      } else {
        setButtonType("error");
        setDefaultErrorMsg(response?.data?.message || "Failed to update list name");
        setDefaultAlert(true);
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message || "Failed to update list name");
      setDefaultAlert(true);
    } finally {
      setIsUpdatingName(false);
    }
  };

  const downloadVerifiedData = async (params, updateParams) => {
    await PostWithTokenNoCache(ApiName.passingRecordIds, params)
      .then(function (response) {
        if (response.data.status === 200) {

          setDownloadedRows(JSON.parse(response.data.data));
          updateWishListStatus(updateParams);

          setTimeout(() => {
            csvLinkRef.current.link.click();
          }, 1)

          setTimeout(() => {
            setDownloadedRows([]);
          }, 3000)
        }
      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
        setIsLoading(false);
      });
  }

  const downloadCompanyDataCsvInChunks = async (csvData) => {
    try {
      // Create a writable stream for the file
      const fileStream = streamSaver.createWriteStream('reachstreamDownload.csv', {
        size: csvData.length, // Optional: Specify the total size of the file
      });
      const writer = fileStream.getWriter();

      const encoder = new TextEncoder();
      const chunkSize = 1000; // Number of rows per chunk
      const rows = csvData.split('\n'); // Split CSV data into rows

      // Write the header row
      if (rows.length > 0) {
        await writer.write(encoder.encode(rows[0] + '\n'));
      }

      // Process rows in chunks
      for (let i = 1; i < rows.length; i += chunkSize) {
        const chunk = rows.slice(i, i + chunkSize).join('\n') + '\n';
        await writer.write(encoder.encode(chunk)); // Write each chunk
      }

      // Close the writer
      await writer.close();
      return "Downloaded Successfully";

    } catch (error) {
      throw new Error("Failed to download CSV");
    }
  };

  const downloadCsvInChunks = async (csvData) => {
    try {
      // Create a writable stream for the file
      const fileStream = streamSaver.createWriteStream('reachstreamDownload.csv');
      const writer = fileStream.getWriter();

      const encoder = new TextEncoder();
      const chunkSize = 1000; // Number of rows per chunk
      const rows = csvData.split('\n'); // Split CSV data into rows

      // Write the header row
      if (rows.length > 0) {
        writer.write(encoder.encode(rows[0] + '\n'));
      }

      // Process rows in chunks
      for (let i = 1; i < rows.length; i += chunkSize) {
        const chunk = rows.slice(i, i + chunkSize).join('\n') + '\n';
        writer.write(encoder.encode(chunk)); // Write each chunk
      }

      // Close the writer
      await writer.close();
      return "Downloaded Successfully";

    } catch (error) {
      throw new Error("Failed to download CSV");
    }
  };

  const companyWishListReadyToDownload = async (id) => {
    setIsLoading(true);

    const params = { wishListId: id };
    try {
      const response = await PostWithTokenNoCache(ApiName.downloadCompanyWishList, params);
      const jsonData = JSON.parse(response.data.data); // Parse the stringified JSON data
      if (!jsonData || jsonData.length === 0) {
        setCsvDownloadMessage("No data available to download.");
      } else {
        // Filter the data to include only the specified headers
        const filteredData = jsonData.map((item) => ({
          "Company Name": item.company_company_name,
          "Website": item.company_website,
          "Company Phone Number": item.company_phone_1,
          "Company Employee Size": item.company_employee_size,
          "Company Annual Revenue Amount": item.company_annual_revenue_amount,
          "Company Industries": item.company_industries,
          "Address": item.company_address_street,
          "City": item.company_address_city,
          "State": item.company_address_state,
          "Zipcode": item.company_address_zipcode,
          "Country": item.company_address_country,
          "Siccode": item.company_sic_code,
        }));


        // Convert the filtered data into CSV format
        const csvData = Papa.unparse(filteredData, {
          header: true,
        });

        // Use the chunked download function
        const message = await downloadCompanyDataCsvInChunks(csvData);
        setCsvDownloadMessage(message);
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error.message || "Please try again!");
      setDefaultAlert(true);
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setCsvDownloadMessage("");
      }, 3000);
    }
  };

  const readyToDownload = async (id) => {
    setIsLoading(true);

    const params = { wish_list_id: id };

    try {
      const response = await PostWithTokenNoCache(ApiName.downloadContactData, params);
      const csvData = response.data;

      if (!csvData || csvData.trim() === "") {
        setCsvDownloadMessage("No data available to download.");
      } else {
        // Use the chunked download function
        const message = await downloadCsvInChunks(csvData);
        setCsvDownloadMessage(message);
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error.message || "Please try again!");
      setDefaultAlert(true);
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setCsvDownloadMessage("");
      }, 3000);
    }
  };

  const checkVerifiedData = async (id, listName, wishListSection, listCount) => {

    setIsLoading(true);
    const params = {
      wishListId: id,
    };
    await PostWithTokenNoCache(ApiName.revealEmailByWishList, params)
      .then(async function (response) {
        if (response.data.status === 200 && response.data.message === "success") {

          let data = JSON.parse(response.data.data);
          let downloadIds = data
            .filter(item => item.verifierStatus === "Valid" || item.verifierStatus === "Guaranteed")
            .map(item => item.dataId);

          const downloadIdsWithoutDuplicates = new Set([...downloadIds]);

          // Convert the Set back to an array
          const downloadIdsArray = [...downloadIdsWithoutDuplicates];

          const updateParams = {
            id,
            listName,
            wishListSection,
            listCount,
            status: "DOWNLOADED"
          }

          let downloadParams = {
            "wishListId": id,
            "searchBy": selectedWishList === "CONTACT" ? "contact" : "company",
            "ids": { ...downloadIdsArray }
          }
          await downloadVerifiedData(downloadParams, updateParams);
        }
      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message ? errors?.response?.data?.message : "Please try again.!");
        setDefaultAlert(true);
        setIsLoading(false);
      });

  }

  const updateWishListStatus = async (data) => {
    const params = {
      id: data.id,
      listName: data.listName,
      wishListSection: data.wishListSection,
      status: data.status,
      listCount: data.listCount
    };
    await PostWithTokenNoCache(ApiName.updateList, params)
      .then(function (response) {
        if (response.data.status === 200) {
          setIsLoading(false);
          getAllList(selectedWishList);
        }
      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
        setIsLoading(false);
      });
  }

  const revealMailByWishList = async (id, listName, wishListSection, listCount) => {
    if (parseInt(listCount) > parseInt(userCredits)) {
      setButtonType("credit-message");
      setDefaultErrorMsg("Insufficient Credits to perform this action.");
      setDefaultAlert(true);
      setIsLoading(false);

      return false;
    }
    setIsLoading(true);
    setIsReveal(true);

    const params = {
      wishListId: id,
    };
    await PostWithTokenNoCache(ApiName.processWithWishlistId, params)
      .then(async function (response) {
        if (response.data.status === 200 && response.data.message === "Processing started.") {
          setShowVerificationModal(true);
          setIsLoading(false);
          setTimeout(() => {
            setIsReveal(false);
          }, 10000)
          await getAllList(selectedWishList);
        } else if (response.data.status === 200 && response.data.message === "success") {
          checkVerifiedData(id, listName, wishListSection, listCount);
        }

      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message ? errors?.response?.data?.message : "Please try again.!");
        setDefaultAlert(true);
        setIsLoading(false);
        setIsReveal(false);
      });
  }

  // Handle tab click event
  const handleContactTabClick = () => {
    setIsLoading(true);
    setData([]);
    setSelectedWishList("CONTACT");
    getAllList("CONTACT");
  };
  const handleCompanyTabClick = () => {
    setIsLoading(true);
    setData([]);
    setSelectedWishList("COMPANY");
    getAllList("COMPANY");
  };

  const getStatusButton = (item) => {
    // Check if this specific item is being edited
    const isCurrentItemBeingEdited = editingListId === item.id;

    // Hide action buttons completely when in edit mode
    if (isCurrentItemBeingEdited) {
      return null;
    }

    if (item.status === "IN_PROGRESS" || item.status === "PENDING") {
      return (
        <button className="verifying-btn">
          <img src="images/saved-list-verify.png" className="img-fluid rotate-animation" /> Verifying
        </button>
      );
    } else if (item.status === "VERIFIED" || item.status === "DOWNLOADED") {
      return (
        <button
          className="ready-btn"
          onClick={() => readyToDownload(item.id)}
        >
          Ready to download
        </button>
      );
    } else if (item.status === "NOT_READY_FOR_VERIFICATION") {
      return (
        <button className="verifying-btn">
          <img src="images/saved-list-verify.png" className="img-fluid rotate-animation" /> Please wait
        </button>
      );
    } else if (item.status === "OPEN" && item.listCount == 0) {
      return (
        <>
          <div
            className="hover-button-wrapper"
            onMouseEnter={() => setHoveredItemId(item.id)}
            onMouseLeave={() => setHoveredItemId(null)}
          >
            <button className="verifying-btn-please">
              Please add...
            </button>

            {hoveredItemId === item.id && (
              <p className="popup-fade">
                Please add the data and try again
              </p>
            )}
          </div>
        </>
      );
    } else if (item.status === "INTERNAL_ERROR") {
      return (
        <button className="verifying-btn-please">
          Failed. Try again with another list
        </button>
      );
    } else {
      return (
        <button
          className="verify-btn"
          onClick={() => revealMailByWishList(item.id, item.listName, item.wishListSection, item.listCount)}
          disabled={isReveal}
          style={{
            opacity: isReveal ? 0.5 : 1,
            cursor: isReveal ? 'not-allowed' : 'pointer'
          }}
        >
          Verify and Download
        </button>
      );
    }
  };

  return (
    <>
      <Header />
      <div className="custom-tabs-container">

      
        {isLoading && (
          <div className="mx-auto mt-5" style={{ textAlign: "center" }}>
            <img src={loadingGif} alt="Loading" className="loader" width="400" />
          </div>
        )}

        {!isLoading && (
          <>
            {isReveal && showVerificationModal && (
              <div className="modal fade show" style={{ display: 'block' }}>
                
                <div className="modal-dialog modal-dialog-centered" style={{ width: "500px" }}>
                  <div className="modal-content">
                    <div className="modal-header" style={{ border: "0", padding: "0px 10px 24px 0px" }}>
                      <button
                        type="button"
                        className="close-verification"
                        style={{ position: "absolute", right: "8px", backgroundColor: "#fff", border: "0", fontSize: "26px", color: "#55c2c3", outline: "none", cursor: "pointer" }}
                        onClick={() => setShowVerificationModal(false)}
                        aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                      </button>
                    </div>
                    <div className="modal-body">
                      <p className="VerificationInprogress">Verification Inprogress</p>
                      <p className="receiveyourlist">You will receive your list on your registered email address shortly. Alternatively, you can return to this page to download your list once the verification is completed.</p>
                      <p className="noted"><span style={{ color: "#55C2C3" }}>Note:&nbsp;&nbsp;</span>Don't Worry! We will only deduct credits for valid emails.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="tabs-wrapper">
            <h6 className="may-my-account">Saved List</h6>
              <div className="d-flex flex-row justify-content-between" style={{ borderBottom: "1px solid #ddd" }}>
                <div className="custom-tab-header">
                  <button
                    className={`custom-tab-button ${selectedWishList === 'CONTACT' ? 'is-active' : ''}`}
                    onClick={() => handleContactTabClick()}
                  >
                    <img src="images/companys.png" width="14" /> &nbsp;
                    <span>Contacts</span>
                  </button>
                  <button
                    className={`custom-tab-button ${selectedWishList === 'COMPANY' ? 'is-active' : ''}`}
                    onClick={() => handleCompanyTabClick()}
                  >
                    <img src="images/contacts.png" width="14" /> &nbsp;
                    <span>Companies</span>
                  </button>
                </div>

                <div>
                  <button className="create-new-list" onClick={() => setIsOpenCreateList(true)}><img src="images/plus-sign.png" /> Create New List</button>
                </div>
              </div>
              <div className="tab-content">
                {data.length === 0 ? (
                  <EmptyList selectedWishList={selectedWishList === 'CONTACT' ? 'CONTACT' : 'COMPANY'} />
                ) : (
                  <>
                    {selectedWishList === 'CONTACT' && (
                      <div className="custom-table-container">
                        <table className="custom-table">
                          <thead>
                            <tr>
                              <th>List Name</th>
                              <th>Creation Date</th>
                              <th>Last Updated</th>
                              <th>Records</th>
                              <th>Total Valid Emails</th>
                              <th style={{ textAlign: "center" }}>Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {data.map((item, index) => (
                              <tr key={index}>
                                <td className="contact-list-plan">
                                  {editingListId === item.id ? (
                                    <input
                                      type="text"
                                      value={editedListName}
                                      onChange={(e) => setEditedListName(e.target.value)}
                                      className="form-control"
                                      style={{ maxWidth: "200px" }}
                                      disabled={isUpdatingName}
                                    />
                                  ) : (
                                    item.listName
                                  )}
                                </td>
                                <td>{dateFormat(item.createdAt)}</td>
                                <td>{dateFormat(item.updatedAt)}</td>
                                <td>{item.listCount}</td>
                                <td>{item.validEmailCount || '-'}</td>
                                <td className="actions-cell">
                                  {getStatusButton(item)}
                                  {editingListId === item.id ? (
                                    <>
                                      <img
                                        src="images/check.png"
                                        className="img-fluid"
                                        onClick={() => handleSaveEdit(item.id)}
                                        style={{ cursor: 'pointer', opacity: isUpdatingName ? 0.5 : 1 }}
                                        title="Save"
                                        alt="Save"
                                      />
                                      <img
                                        src="images/cancl.png"
                                        className="img-fluid"
                                        onClick={handleCancelEdit}
                                        style={{ cursor: 'pointer' }}
                                        title="Cancel"
                                        alt="Cancel"
                                      />
                                    </>
                                  ) : (
                                    <>
                                      <img
                                        src="images/Edit-pen.png"
                                        className="img-fluid"
                                        onClick={() => handleEditClick(item)}
                                        style={{ cursor: 'pointer' }}
                                        title="Edit"
                                        alt="Edit"
                                      />
                                      <img
                                        src="images/view-saved-list.png"
                                        className="img-fluid"
                                        onClick={() => viewListDetails(item.id, item.wishListSection)}
                                        style={{ cursor: 'pointer' }}
                                        title="View"
                                        alt="View"
                                      />
                                      <img
                                        src="images/saved-list-delete.png"
                                        className="img-fluid"
                                        onClick={() => deleteList(item.id)}
                                        style={{ cursor: 'pointer' }}
                                        title="Delete"
                                        alt="Delete"
                                      />
                                    </>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                    {selectedWishList === 'COMPANY' && (
                      <div className="custom-table-container">
                        <table className="custom-table">
                          <thead>
                            <tr>
                              <th>List Name</th>
                              <th>Creation Date</th>
                              <th>Last Updated</th>
                              <th>Total Valid Emails</th>
                              <th style={{ textAlign: "center" }}>Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {data.map((item, index) => (
                              <tr key={index}>
                                <td className="contact-list-plan">
                                  {editingListId === item.id ? (
                                    <input
                                      type="text"
                                      value={editedListName}
                                      onChange={(e) => setEditedListName(e.target.value)}
                                      className="form-control"
                                      style={{ maxWidth: "200px" }}
                                      disabled={isUpdatingName}
                                    />
                                  ) : (
                                    item.listName
                                  )}
                                </td>
                                <td>{dateFormat(item.createdAt)}</td>
                                <td>{dateFormat(item.updatedAt)}</td>
                                <td>{item.validEmailCount || '-'}</td>
                                <td className="actions-cell">
                                  {editingListId !== item.id && (
                                    <button
                                      className="ready-btn"
                                      onClick={() => companyWishListReadyToDownload(item.id)}
                                    >
                                      Download
                                    </button>
                                  )}
                                  {editingListId === item.id ? (
                                    <>
                                      <img
                                        src="images/check.png"
                                        className="img-fluid"
                                        onClick={() => handleSaveEdit(item.id)}
                                        style={{ cursor: 'pointer', opacity: isUpdatingName ? 0.5 : 1 }}
                                        title="Save"
                                        alt="Save"
                                      />
                                      <img
                                        src="images/cancl.png"
                                        className="img-fluid"
                                        onClick={handleCancelEdit}
                                        style={{ cursor: 'pointer' }}
                                        title="Cancel"
                                        alt="Cancel"
                                      />
                                    </>
                                  ) : (
                                    <>
                                      <img
                                        src="images/Edit-pen.png"
                                        className="img-fluid"
                                        onClick={() => handleEditClick(item)}
                                        style={{ cursor: 'pointer' }}
                                        title="Edit"
                                        alt="Edit"
                                      />
                                      <img
                                        src="images/view-saved-list.png"
                                        className="img-fluid"
                                        onClick={() => viewListDetails(item.id, item.wishListSection)}
                                        style={{ cursor: 'pointer' }}
                                        title="View"
                                        alt="View"
                                      />
                                      <img
                                        src="images/saved-list-delete.png"
                                        className="img-fluid"
                                        onClick={() => deleteList(item.id)}
                                        style={{ cursor: 'pointer' }}
                                        title="Delete"
                                        alt="Delete"
                                      />
                                    </>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </>
                )}

                <>
                  {data.length > 0 && (
                    <Pagination
                      currentPage={currentPage}
                      totalCount={totalPages * pageSize}
                      pageSize={pageSize}
                      onPageChange={(page) => setCurrentPage(page)}
                    />
                  )}
                </>
              </div>

            </div>



            {csvDownloadMessage && (
              <div className="successmessage">
                <p className="messages">
                  <img src="../images/promocode.png" width="20" alt="Download Success" />&nbsp;{csvDownloadMessage}
                </p>
              </div>
            )}

            {defaultAlert && defaultErrorMsg && (
              buttonType === "credit-message" ? (
                <CreditAlert data={defaultErrorMsg} />
              ) : (
                <Alert data={defaultErrorMsg} />
              )
            )}

            <CSVLink
              styles={{ display: "none" }}
              data={downloadedRows}
              filename="reachstreamDownload.csv"
              ref={csvLinkRef}
            />
          </>
        )}

        {isPopupOpen && (
          <DeletePopup
            onCloseDelete={onCloseDelete}
            onDeleteSuccess={onDeleteSuccess}
          />
        )}

        {isOpenCreateList && (
          <CreateListPopup
            setIsOpenCreateList={setIsOpenCreateList}
            getAllList={() => getAllList(selectedWishList)}
            currentWishListSection={selectedWishList}
          />
        )}
      </div>
    </>
  );
};

export default SavedList;
